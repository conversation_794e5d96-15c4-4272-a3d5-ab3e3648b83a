import React, { useState, Ref, useEffect } from 'react';
import { Button, Tabs, Table, Space, Popconfirm } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { YTHPickUser } from 'yth-ui';
import { CurrentUser } from '@/Constant';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import { Unit, User } from '@/service/system';
import baseApi from '@/service/baseApi';
import { IAction, IUserProps } from 'yth-ui/es/components/pickUser';
import style from './index.module.less';
import { ColumnsType } from 'antd/lib/table';

const { TabPane } = Tabs;

// 定义学习对象类型
interface DicDataType {
  code: string;
  text: string;
}

// 扩展IUserProps接口，包含更多用户信息
interface ExtendedUserProps extends IUserProps {
  phone?: string;
  userCode?: string;
}

const defaultQueryData: { learningObject: DicDataType[] } = {
  learningObject: [
    { code: 'A08A39A01', text: '主要负责人' },
    { code: 'A08A39A02', text: '安全管理人员' },
    { code: 'A08A39A03', text: '员工' },
    { code: 'A08A39A04', text: '承包商' },
  ],
};

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    learningObject?: DicDataType[];
  };
};

// 扩展用户属性接口
interface ExtendedUserProps extends IUserProps {
  phone?: string;
  userCode?: string;
}

// 考核人员项接口
interface AssessmentPersonnelItem {
  key: string;
  name: string;
  objectType: string;
  employeeId: string;
  phone: string;
}

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery = {},
}) => {
  // 获取learningObject数据，优先使用传入的defaultQuery，否则使用默认数据
  const learningObjectData: DicDataType[] =
    defaultQuery?.learningObject || defaultQueryData.learningObject;

  // 当前激活的tab key - 直接初始化为第一个选项的code
  const [activeTabKey, setActiveTabKey] = useState<string>(
    learningObjectData && learningObjectData.length > 0 ? learningObjectData[0].code : '',
  );

  // 选中用户的数据
  const [userValue, setUserValue] = useState<IUserProps[]>([]);

  // 使用状态管理所有tab的人员数据
  const [personnelData, setPersonnelData] = useState<Record<string, AssessmentPersonnelItem[]>>({});

  // 用户选择组件的ref
  const PickRef: Ref<IAction> = React.useRef();

  // 确保activeTabKey有值（防御性编程）
  useEffect(() => {
    if (learningObjectData && learningObjectData.length > 0 && !activeTabKey) {
      setActiveTabKey(learningObjectData[0].code);
    }
  }, [learningObjectData, activeTabKey]);

  // tab切换处理函数
  const handleTabChange: (key: string) => void = (key: string) => {
    setActiveTabKey(key);
    console.log('切换到tab:', key, '当前人员数据:', personnelData);
  };
  // 删除人员
  const handleDeletePerson: (tabKey: string, personKey: string) => void = (
    tabKey: string,
    personKey: string,
  ) => {
    setPersonnelData((prev) => ({
      ...prev,
      [tabKey]: (prev[tabKey] || []).filter((item) => item.key !== personKey),
    }));
  };

  // 获取Table的列配置
  const getTableColumns: (tabKey: string) => ColumnsType<AssessmentPersonnelItem> = (tabKey) => [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'objectType',
      key: 'objectType',
      width: 120,
    },
    {
      title: '工号',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 120,
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: string, record: AssessmentPersonnelItem) => (
        <Space size="middle">
          <Popconfirm
            title="确定要删除这个人员吗？"
            onConfirm={() => handleDeletePerson(tabKey, record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 渲染tab内容
  const renderTabContent: (tabItem: DicDataType) => React.ReactNode = (tabItem) => {
    const currentData: AssessmentPersonnelItem[] = personnelData[tabItem.code] || [];

    return (
      <div>
        <div className={style['add-btn-container']}>
          <Button
            type="primary"
            onClick={() => {
              PickRef?.current?.click();
            }}
          >
            添加
          </Button>
        </div>
        <Table<AssessmentPersonnelItem>
          columns={getTableColumns(tabItem.code)}
          dataSource={currentData}
          pagination={false}
          size="small"
          bordered
          locale={{ emptyText: '暂无数据' }}
        />
      </div>
    );
  };

  return (
    <div
      className={style['assessment-personnel-container']}
      style={{ width: '100%', height: '100%' }}
    >
      <div>
        <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
          {learningObjectData.map((item: DicDataType) => (
            <TabPane
              tab={
                <span>
                  {item.text}
                  {/* 显示当前tab的数据数量 */}
                  {personnelData[item.code] && personnelData[item.code].length > 0 && (
                    <span style={{ marginLeft: 4, color: '#1890ff' }}>
                      ({personnelData[item.code].length})
                    </span>
                  )}
                </span>
              }
              key={item.code}
            >
              {/* Tab内容现在只显示当前激活的tab，但所有表单数据都保存在同一个form中 */}
            </TabPane>
          ))}
        </Tabs>

        {/* 渲染当前激活tab的内容 */}
        {activeTabKey && (
          <div>
            {learningObjectData
              .filter((item) => item.code === activeTabKey)
              .map((item) => renderTabContent(item))}
          </div>
        )}
      </div>
      <div className={style['pick-user-container']}>
        <YTHPickUser
          defaultOrganize={{
            id: CurrentUser()?.accountId,
            name: '',
            type: 'org',
          }}
          value={userValue}
          actionRef={PickRef}
          requestOrganize={async () => {
            const resData: Unit = await baseApi.getUnitTree();
            return formatTree(resData, 'unitType', 'unitName');
          }}
          searchMode="1"
          multiple
          onOk={(users: ExtendedUserProps[]) => {
            // 根据当前激活的tab获取对应的tab信息
            const currentTabData: DicDataType | undefined = learningObjectData.find(
              (item) => item.code === activeTabKey,
            );
            if (!currentTabData) {
              return;
            }

            // 将选中的用户数据转换为AssessmentPersonnelItem格式
            const formattedUsers: AssessmentPersonnelItem[] = users.map(
              (user: ExtendedUserProps) => ({
                key: user.id || `${Date.now()}-${Math.random()}`, // 生成唯一key
                name: user.name, // 用户姓名
                objectType: currentTabData.text, // 对象类型，根据当前tab设置
                employeeId: user.userCode || user.id, // 优先使用工号，否则使用用户ID
                phone: user.phone || '', // 使用用户的联系电话，如果没有则为空
              }),
            );

            // 添加数据到对应tab的状态中
            setPersonnelData((prev) => ({
              ...prev,
              [currentTabData.code]: [...(prev[currentTabData.code] || []), ...formattedUsers],
            }));
          }}
          remoteSearch
          requestUser={async (organize) => {
            const resData: User[] = await baseApi.getUserList(organize.id);
            const newData: ExtendedUserProps[] = [];
            resData.forEach((item: User) => {
              newData.push({
                id: item.id,
                name: item.realName,
                type: 'user',
                // 扩展用户信息，便于后续表单数据映射
                phone: item.phone,
                userCode: item.userCode,
              });
            });
            return newData;
          }}
        />
      </div>

      <div className={style['btn-container']}>
        <Button
          onClick={() => {
            closeModal?.();
          }}
        >
          关闭
        </Button>
        <Button
          type="primary"
          style={{ marginLeft: '10px' }}
          onClick={() => {
            // 将人员数据转换为原来的格式
            const formattedData: Record<string, string>[] = [];
            Object.keys(personnelData).forEach((tabKey) => {
              const tabData: AssessmentPersonnelItem[] = personnelData[tabKey];
              tabData.forEach((person) => {
                formattedData.push({
                  name: person.name,
                  objectType: person.objectType,
                  employeeId: person.employeeId,
                  phone: person.phone,
                  tabKey, // 添加tab标识
                });
              });
            });
            console.log('formattedData', formattedData);

            onComplete?.(formattedData);
            closeModal?.();
          }}
        >
          保存
        </Button>
      </div>
    </div>
  );
};

export default AssessmentPersonnelList;
